import { config } from "@/config";
import { type Context } from "hono";
import { createMiddleware } from "hono/factory";

function getProxyAuthorizationFromRequest(ctx: Context): string | undefined {
  if (ctx.req.header("Authorization")) {
    const token = ctx.req.header("Authorization")?.slice("Bearer ".length);
    return token;
  }

  if (ctx.req.header("x-api-key")) {
    const token = ctx.req.header("x-api-key");
    return token;
  }

  if (ctx.req.header("x-goog-api-key")) {
    const token = ctx.req.header("x-goog-api-key");
    return token;
  }

  if (ctx.req.query("key")) {
    const token = ctx.req.query("key");
    return token;
  }

  return undefined;
}

export function authenticateMiddleware() {
  return createMiddleware(async (ctx, next) => {
    const isBrowser = ctx.req.header("User-Agent")?.toLowerCase().includes("mozilla");
    if (isBrowser) return ctx.redirect("/");

    const token = getProxyAuthorizationFromRequest(ctx);
    ctx.set("token", token!);

    if (config.gatekeeper === "none") {
      return next();
    }

    if (!token) {
      return Response.json({ error: { message: "Unauthorized" } }, { status: 401 });
    }

    if (config.gatekeeper === "user_token") {
      // TODO: Check user token against database
    }

    return next();
  });
}
