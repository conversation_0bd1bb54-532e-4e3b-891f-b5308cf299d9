CREATE TABLE `proxy_key_stats` (
	`id` text PRIMARY KEY NOT NULL,
	`hash` text NOT NULL,
	`modelFamily` text NOT NULL,
	`inputTokens` integer DEFAULT 0 NOT NULL,
	`outputTokens` integer DEFAULT 0 NOT NULL,
	`reasoningTokens` integer DEFAULT 0 NOT NULL,
	`prompts` integer DEFAULT 0 NOT NULL,
	FOREIGN KEY (`hash`) REFERENCES `proxy_keys`(`hash`) ON UPDATE cascade ON DELETE cascade,
	FOREIGN KEY (`modelFamily`) REFERENCES `proxy_modelFamilies`(`id`) ON UPDATE cascade ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `idx_key_stats_hash_model_family` ON `proxy_key_stats` (`hash`,`modelFamily`);--> statement-breakpoint
CREATE TABLE `proxy_keys` (
	`hash` text PRIMARY KEY NOT NULL,
	`encryptedKey` text NOT NULL,
	`provider` text NOT NULL,
	`modelIds` text DEFAULT '[]' NOT NULL,
	`status` text DEFAULT 'unknown' NOT NULL,
	`disabledAt` integer,
	`disabledBy` text,
	`ratelimitedAt` integer DEFAULT 0 NOT NULL,
	`ratelimitedUntil` integer DEFAULT 0 NOT NULL,
	`lastUsedAt` integer DEFAULT 0 NOT NULL,
	`lastCheckedAt` integer DEFAULT 0 NOT NULL,
	`lastModelsCheckedAt` integer DEFAULT 0 NOT NULL,
	`nextCheckAt` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	`updatedAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	`metadata` text DEFAULT '{}' NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_keys_provider` ON `proxy_keys` (`hash`,`provider`);--> statement-breakpoint
CREATE TABLE `proxy_modelFamilies` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`provider` text NOT NULL,
	`models` text DEFAULT '[]' NOT NULL,
	`inputCost` integer DEFAULT 0 NOT NULL,
	`outputCost` integer DEFAULT 0 NOT NULL,
	`maxContext` integer DEFAULT 0 NOT NULL,
	`maxOutput` integer DEFAULT 0 NOT NULL,
	`ratelimit` integer DEFAULT 0 NOT NULL,
	`refillRate` integer DEFAULT 0 NOT NULL,
	`refillInterval` integer DEFAULT 0 NOT NULL,
	`weight` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL,
	`updatedAt` integer DEFAULT (strftime('%s', 'now')) NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `proxy_modelFamilies_name_unique` ON `proxy_modelFamilies` (`name`);--> statement-breakpoint
CREATE INDEX `idx_models_provider` ON `proxy_modelFamilies` (`provider`);