{
  "compilerOptions": {
    // Environment setup & latest features
    "lib": ["dom", "dom.iterable", "ES2022"],
    "target": "ESNext",
    "module": "Preserve",
    "moduleDetection": "force",
    "jsx": "react-jsx",
    "allowJs": true,

    // Bundler mode
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "noEmit": true,

    // Best practices
    "strict": true,
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,

    // Some stricter flags (disabled by default)
    "noImplicitAny": true,

    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/frontend/components/*"],
      "@/frontend/*": ["./src/frontend/*"],
      "@/public/*": ["./public/*"]
    }
  },
  "exclude": ["node_modules", "dist"],
  "include": ["src", "**/*.ts", "**/*.tsx", "proxy.config.ts", "build.ts"]
}
