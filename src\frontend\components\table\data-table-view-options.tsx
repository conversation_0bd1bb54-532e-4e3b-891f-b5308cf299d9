import type { Table } from "@tanstack/react-table";
import { Check, ChevronsUpDown, Settings2 } from "lucide-react";
import * as React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

import { cn } from "@/shared/utils";

interface DataTableViewOptionsProps<TData> {
  table: Table<TData>;
}

const columnsHeaders: Record<string, string> = {
  // Users
  ip: "IPs",
  type: "Type",
  adminNote: "Admin Notes",
  nickname: "Nickname",
  promptCount: "Prompts",
  tokenCounts: "Tokens",
  sumCost: "Cost",
  promptCountSinceStart: "Prompts (Start)",
  tokenCountsSinceStart: "Tokens (Start)",
  sumCostSinceStart: "Cost (Start)",

  // Keys
  hash: "Key ID",
  provider: "Provider",
  status: "Status",
  modelIds: "Models",
  lastUsedAt: "Last Used",
  createdAt: "Created",
  actions: "Actions",
  tier: "Tier",
  organizationId: "Organization ID",
  awsLoggingStatus: "AWS Logging",
  contentFiltering: "Content Filtering",
  tokens: "Tokens",
  addedAt: "Added At",
  lastChecked: "Last Checked",
  lastUsed: "Last Used At",
  nextCheckAt: "Next Check",
};

export function DataTableViewOptions<TData>({ table }: DataTableViewOptionsProps<TData>) {
  const triggerRef = React.useRef<HTMLButtonElement>(null);

  return (
    <Popover modal>
      <PopoverTrigger asChild>
        <Button
          ref={triggerRef}
          aria-label="Toggle columns"
          variant="outline"
          role="combobox"
          size="sm"
          className="border-border focus:ring-ring ml-auto flex h-8 gap-2 focus:ring-1 focus:outline-none focus-visible:ring-0"
        >
          <Settings2 className="size-4" />
          View
          <ChevronsUpDown className="ml-auto size-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>

      <PopoverContent
        align="end"
        className="w-44 p-0"
        onCloseAutoFocus={() => triggerRef.current?.focus()}
      >
        <Command>
          <CommandInput placeholder="Search columns..." />
          <CommandList>
            <CommandEmpty>No columns found.</CommandEmpty>

            <CommandGroup>
              <CommandItem
                onSelect={() => {
                  const value = !table.getIsAllColumnsVisible();
                  table.toggleAllColumnsVisible(value);
                }}
              >
                <span className="truncate">All Columns</span>
                <Check
                  className={cn(
                    "ml-auto size-4 shrink-0",
                    table.getIsAllColumnsVisible() ? "opacity-100" : "opacity-0",
                  )}
                />
              </CommandItem>
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup>
              {table
                .getAllLeafColumns()
                .filter((column) => typeof column.accessorFn !== "undefined" && column.getCanHide())
                .map((column) => {
                  return (
                    <CommandItem
                      key={column.id}
                      className="hover:cursor-pointer"
                      onSelect={() => {
                        const value = !column.getIsVisible();
                        column.toggleVisibility(value);
                      }}
                    >
                      <span className="truncate">{columnsHeaders[column.id] ?? column.id}</span>
                      <Check
                        className={cn(
                          "ml-auto size-4 shrink-0",
                          column.getIsVisible() ? "opacity-100" : "opacity-0",
                        )}
                      />
                    </CommandItem>
                  );
                })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
