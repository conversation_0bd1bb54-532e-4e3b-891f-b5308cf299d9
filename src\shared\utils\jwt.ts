import type { Context } from "hono";
import { deleteCookie, getSigned<PERSON>ookie, setSignedCookie } from "hono/cookie";
import { sign, verify } from "hono/jwt";

import { SECRET_SIGNING_KEY } from "@/config";
import { tryCatch } from "./try-catch";

type JwtPayload = {
  sub: string;
  username: string;
  type: "access" | "refresh";
  iat?: number;
  exp?: number;
};

type TokenPair = { accessToken: string; refreshToken: string };

const EXPIRES_IN_30_DAYS = 30 * 24 * 60 * 60;

export async function createAccessToken(sub: string, username: string): Promise<string> {
  const payload: JwtPayload = { sub, username, type: "access", exp: EXPIRES_IN_30_DAYS * 1000 };
  return sign(payload, SECRET_SIGNING_KEY);
}

export async function createRefreshToken(sub: string, username: string): Promise<string> {
  const payload: JwtPayload = { sub, username, type: "refresh", exp: EXPIRES_IN_30_DAYS * 1000 };
  return sign(payload, SECRET_SIGNING_KEY);
}

export async function verifyTokenRaw(token: string): Promise<JwtPayload | null> {
  const [decoded, err] = await tryCatch(() => verify(token, SECRET_SIGNING_KEY));
  if (err) {
    console.error(err);
    return null;
  }

  const payload = decoded as unknown as JwtPayload;

  if (
    !payload ||
    typeof payload.sub !== "string" ||
    typeof payload.username !== "string" ||
    !["access", "refresh"].includes(payload.type)
  ) {
    return null;
  }

  const typed: JwtPayload = {
    sub: payload.sub,
    username: payload.username,
    type: payload.type,
    iat: payload.iat,
    exp: payload.exp,
  };

  return typed;
}

export async function issueTokenPair(sub: string, username: string): Promise<TokenPair> {
  const [accessToken, refreshToken] = await Promise.all([
    createAccessToken(sub, username),
    createRefreshToken(sub, username),
  ]);

  return { accessToken, refreshToken };
}

export async function setAuthCookies(ctx: Context, pair: TokenPair) {
  // Signed cookies with httpOnly, secure in prod
  const isProd = process.env.NODE_ENV === "production";
  await setSignedCookie(ctx, "access_token", pair.accessToken, SECRET_SIGNING_KEY, {
    httpOnly: true,
    secure: isProd,
    sameSite: "Lax",
    path: "/",
    maxAge: EXPIRES_IN_30_DAYS,
  });

  await setSignedCookie(ctx, "refresh_token", pair.refreshToken, SECRET_SIGNING_KEY, {
    httpOnly: true,
    secure: isProd,
    sameSite: "Lax",
    path: "/",
    maxAge: EXPIRES_IN_30_DAYS,
  });
}

export async function clearAuthCookies(ctx: Context) {
  deleteCookie(ctx, "access_token", { path: "/" });
  deleteCookie(ctx, "refresh_token", { path: "/" });
}

export async function getAccessFromCookie(ctx: Context) {
  const token = await getSignedCookie(ctx, SECRET_SIGNING_KEY, "access_token");
  if (!token) return null;

  return verifyTokenRaw(token);
}

export async function getRefreshFromCookie(ctx: Context) {
  const token = await getSignedCookie(ctx, SECRET_SIGNING_KEY, "refresh_token");
  if (!token) return null;

  return verifyTokenRaw(token);
}
