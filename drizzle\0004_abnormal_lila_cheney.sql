ALTER TABLE `proxy_modelFamilies` RENAME COLUMN "weight" TO "ratelimitCost";--> statement-breakpoint
DROP INDEX "idx_key_stats_hash_model_family";--> statement-breakpoint
DROP INDEX "idx_keys_provider";--> statement-breakpoint
DROP INDEX "proxy_modelFamilies_name_unique";--> statement-breakpoint
DROP INDEX "idx_models_provider";--> statement-breakpoint
DROP INDEX "proxy_users_username_unique";--> statement-breakpoint
ALTER TABLE `proxy_modelFamilies` ALTER COLUMN "ratelimitCost" TO "ratelimitCost" integer NOT NULL DEFAULT 1;--> statement-breakpoint
CREATE UNIQUE INDEX `idx_key_stats_hash_model_family` ON `proxy_key_stats` (`hash`,`modelFamily`);--> statement-breakpoint
CREATE INDEX `idx_keys_provider` ON `proxy_keys` (`hash`,`provider`);--> statement-breakpoint
CREATE UNIQUE INDEX `proxy_modelFamilies_name_unique` ON `proxy_modelFamilies` (`name`);--> statement-breakpoint
CREATE INDEX `idx_models_provider` ON `proxy_modelFamilies` (`provider`);--> statement-breakpoint
CREATE UNIQUE INDEX `proxy_users_username_unique` ON `proxy_users` (`username`);--> statement-breakpoint
ALTER TABLE `proxy_modelFamilies` ADD `status` text DEFAULT 'disabled' NOT NULL;--> statement-breakpoint
ALTER TABLE `proxy_modelFamilies` DROP COLUMN `ratelimit`;--> statement-breakpoint
ALTER TABLE `proxy_modelFamilies` DROP COLUMN `refillRate`;--> statement-breakpoint
ALTER TABLE `proxy_modelFamilies` DROP COLUMN `refillInterval`;