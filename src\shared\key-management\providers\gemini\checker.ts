import { tryCatch } from "@/shared/utils/try-catch";

import { KeyCheckerBase } from "@/shared/key-management/base-checker";
import { CheckFailedError } from "@/shared/key-management/error";
import { decrypt } from "@/shared/key-management/utils";

import { type <PERSON><PERSON><PERSON>, Gemini<PERSON>eyProvider } from "./provider";

const BASE_URL = "https://generativelanguage.googleapis.com";

const LIST_MODELS_URL = `${BASE_URL}/v1beta/models`;
const GENERATE_CONTENT_URL = `${BASE_URL}/v1beta/models/gemini-2.0-flash-lite:generateContent`;
const TIER_CHECKING_URL = `${BASE_URL}/v1beta/models/imagen-3.0-generate-002:predict`;

const MIN_CHECK_INTERVAL = 3 * 1000; // 3 seconds
const KEY_CHECK_PERIOD = 60 * 60 * 1000; // 1 hour

type ListModelsResponse = {
  models: { name: string; baseModelId: string; displayName: string; description: string }[];
  nextPageToken: string;
};

type BillingDataError = {
  error: { code: number; message: string; status: "INVALID_ARGUMENT" };
};

type GeminiError = {
  error: { code: string; message: string; status: string; details: unknown[] };
};

export class GeminiKeyChecker extends KeyCheckerBase<GeminiKey> {
  constructor({ updateFn }: { updateFn: typeof GeminiKeyProvider.prototype.update }) {
    super({
      updateFn,
      provider: GeminiKeyProvider.provider,
      keyCheckPeriod: KEY_CHECK_PERIOD,
      minCheckInterval: MIN_CHECK_INTERVAL,
    });
  }

  protected override createHeaders(key: GeminiKey, extras: Record<string, string> = {}): Headers {
    const headers = new Headers();
    headers.set("X-Goog-Api-Key", decrypt(key.encryptedKey));

    Object.entries(extras).forEach(([key, value]) => {
      headers.set(key, value);
    });

    return headers;
  }

  private async getModelIds(key: GeminiKey): Promise<string[]> {
    const response = await fetch(`${LIST_MODELS_URL}?pageSize=1000`, {
      headers: this.createHeaders(key),
    });

    if (!response.ok) {
      throw new CheckFailedError("Failed to get models", response.status, response);
    }

    const [data, err] = await tryCatch(() => response.json() as Promise<ListModelsResponse>);
    if (err) {
      throw new CheckFailedError("Failed to parse models", response.status, response);
    }

    const modelIds = new Set<string>(data.models.map(({ name }) => name.replace("models/", "")));
    return Array.from(modelIds);
  }

  private async testLiveness(key: GeminiKey) {
    const payload = {
      contents: [{ parts: { text: "hello" }, role: "user" }],
      generationConfig: { maxOutputTokens: 1 },
    };

    const response = await fetch(GENERATE_CONTENT_URL, {
      method: "POST",
      body: JSON.stringify(payload),
      headers: this.createHeaders(key, { "Content-Type": "application/json" }),
    });

    if (!response.ok) {
      throw new CheckFailedError("Failed to test liveness", response.status, response);
    }
  }

  private async testTiers(key: GeminiKey): Promise<"free" | "paid"> {
    // Invalid body to not wasting actual credit
    const payload = { model: "models/imagen-3.0-generate-002" };

    const res = await fetch(TIER_CHECKING_URL, {
      method: "POST",
      body: JSON.stringify(payload),
      headers: this.createHeaders(key, { "Content-Type": "application/json" }),
    });

    const [body] = await tryCatch(() => res.json() as Promise<BillingDataError>);
    if (body && body.error.code === 400 && body.error.message.toLowerCase().includes("empty")) {
      return "paid";
    }

    return "free";
  }

  protected override async testKeyOrFail(key: GeminiKey, forced?: boolean) {
    const requireFullCheck = key.status === "unknown" || forced;
    const requireModelCheck =
      key.lastModelsCheckedAt < Date.now() - 24 * 60 * 60 * 1000 && !requireFullCheck;

    const updates: Partial<GeminiKey> = {};

    if (requireFullCheck) {
      const [, modelIds, tier] = await Promise.all([
        this.testLiveness(key),
        this.getModelIds(key),
        this.testTiers(key),
      ]);

      updates.status = "working";
      updates.metadata = { ...key.metadata, tier };
      updates.modelIds = modelIds.filter((id) => !id.includes("ft")).sort();

      updates.lastModelsCheckedAt = Date.now();
    }

    if (requireModelCheck) {
      updates.modelIds = await this.getModelIds(key);
      updates.lastModelsCheckedAt = Date.now();
    }

    return { ...updates, nextCheckAt: null };
  }

  protected override async handleCheckingError(
    key: GeminiKey,
    err: Error | CheckFailedError,
  ): Promise<Partial<GeminiKey>> {
    if (err instanceof CheckFailedError) {
      const [parsed, parseErr] = await tryCatch(() => err.response.json() as Promise<GeminiError>);
      const status = err.status;

      if (parseErr || !parsed || !parsed.error) {
        this.logger.error(
          { key: key.hash, error: err, parseErr },
          "Failed to parse error response while checking key. Scheduling recheck in ~60s.",
        );
        return { nextCheckAt: Date.now() + 60 * 1000 };
      }

      switch (status) {
        case 400: {
          const keyDeadMsgs = [
            "please enable billing",
            "API key not valid",
            "API key expired",
            "pass a valid API",
          ];
          const text = JSON.stringify(parsed.error);
          const regex = new RegExp(`${keyDeadMsgs.join("|")}`, "gi");

          if (text.match(regex)) {
            this.logger.warn(
              { key: key.hash, error: text },
              "Key check returned a non-transient 400 error. Disabling key.",
            );

            return { status: "revoked", disabledBy: "key-checker", disabledAt: Date.now() };
          }
          break;
        }

        case 401:
        case 403: {
          this.logger.warn(
            { key: key.hash, status, error: parsed.error },
            "Key check returned Forbidden/Unauthorized error. Disabling key.",
          );
          return { status: "revoked", disabledBy: "key-checker", disabledAt: Date.now() };
        }

        case 429: {
          const text = JSON.stringify(parsed.error);

          const quotaExhaustedMsgs = ["Resource has been exhausted", '"quota_limit_value":"0"'];
          const quotaRegex = new RegExp(`${quotaExhaustedMsgs.join("|")}`, "gi");

          const rateLimitMsgs = ["GenerateContentRequestsPerMinutePerProjectPerRegion"];
          const rateLimitRegex = new RegExp(`${rateLimitMsgs.join("|")}`, "gi");

          if (text.match(quotaRegex)) {
            this.logger.warn(
              { key: key.hash, error: text },
              "Key check returned a quota exhausted error. Disabling key.",
            );
            return { status: "out_of_quota", disabledBy: "key-checker", disabledAt: Date.now() };
          }

          if (text.match(rateLimitRegex)) {
            this.logger.warn(
              { key: key.hash, error: text },
              "Key check returned a rate limit error. Disabling key.",
            );
            return { status: "revoked", disabledBy: "key-checker", disabledAt: Date.now() };
          }

          this.logger.warn(
            { key: key.hash, status, parsed },
            "Key is rate limited. Rechecking key in 1 minute.",
          );
          return { nextCheckAt: Date.now() + 60 * 1000 };
        }

        case 503: {
          if (
            parsed.error.status === "UNAVAILABLE" &&
            parsed.error.message.includes("overloaded")
          ) {
            this.logger.warn(
              { key: key.hash, status, parsed },
              "Key check returned a 503 error. Rechecking key in 1 minute.",
            );
            return { nextCheckAt: Date.now() + 60 * 1000 };
          }

          break;
        }
      }
    }

    this.logger.error(
      { key: key.hash, error: err.message },
      "Network error while checking key; retrying in ~60s.",
    );
    return { nextCheckAt: Date.now() + 60 * 1000 };
  }
}
