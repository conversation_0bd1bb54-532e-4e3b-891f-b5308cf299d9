import { createCipheriv, createDecipheriv, createHash, randomBytes } from "node:crypto";

import { SECRET_SIGNING_KEY } from "@/config";
import type { Key } from "./index";

/**
 * Given a list of keys, returns a new list of keys sorted from highest to
 * lowest priority.  Keys are prioritized in the following order:
 *
 * 1. Keys which are not rate limited
 *    - If all keys were rate limited recently, select the least-recently
 *       rate limited key.
 *    - Otherwise, select the first key.
 * 2. Keys which have not been used in the longest time
 * 3. Keys according to the custom comparator, if provided
 * @param keys The list of keys to sort
 * @param customComparator A custom comparator function to use for sorting
 */
export function prioritizeKeys<T extends Key>(
  keys: T[],
  customComparator?: (a: T, b: T) => number,
) {
  const now = Date.now();

  return keys.sort((a, b) => {
    const aRateLimited = now < a.ratelimitedUntil;
    const bRateLimited = now < b.ratelimitedUntil;

    if (aRateLimited && !bRateLimited) return 1;
    if (!aRateLimited && bRateLimited) return -1;
    if (aRateLimited && bRateLimited) return a.ratelimitedUntil - b.ratelimitedUntil;

    if (customComparator) {
      const result = customComparator(a, b);
      if (result !== 0) return result;
    }

    return a.lastUsedAt - b.lastUsedAt;
  });
}

const ALGORITHM = "aes-256-gcm";
const IV_LENGTH = 16; // For AES, this is always 16
const AUTH_TAG_LENGTH = 16;

/**
 * Derives a secure 32-byte key from the master secret using SHA-256.
 * @param {string} secret - The master secret to use.
 * @returns {Buffer} A 32-byte buffer representing the derived key.
 */
function getDerivedKey(secret: string): Buffer {
  return createHash("sha256").update(secret).digest();
}

const ENCRYPTION_KEY = getDerivedKey(SECRET_SIGNING_KEY);

/**
 * Encrypts a plaintext string using the derived ENCRYPTION_KEY.
 * @param {string} textToEncrypt - The plaintext to encrypt.
 * @returns {string} The encrypted data, as a hex-encoded string.
 */
export function encrypt(textToEncrypt: string): string {
  const iv = randomBytes(IV_LENGTH);
  const cipher = createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv);

  const encrypted = Buffer.concat([cipher.update(textToEncrypt, "utf8"), cipher.final()]);
  const authTag = cipher.getAuthTag();

  return Buffer.concat([iv, authTag, encrypted]).toString("hex");
}

/**
 * Decrypts an AES-256-GCM encrypted string using the derived ENCRYPTION_KEY.
 * @param {string} encryptedText - The hex-encoded encrypted data.
 * @returns {string} The original decrypted plaintext.
 */
export function decrypt(encryptedText: string): string {
  const encryptedBuffer = Buffer.from(encryptedText, "hex");

  const iv = encryptedBuffer.subarray(0, IV_LENGTH);
  const authTag = encryptedBuffer.subarray(IV_LENGTH, IV_LENGTH + AUTH_TAG_LENGTH);
  const encrypted = encryptedBuffer.subarray(IV_LENGTH + AUTH_TAG_LENGTH);

  const decipher = createDecipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
  decipher.setAuthTag(authTag);

  const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);

  return decrypted.toString("utf8");
}
