import { Hono, type Context } from "hono";

import { createProxy } from "@/lib/middlewares/create-proxy";
import { getDeepSeekSchema } from "@/lib/schemas/input-deepseek";

import { createModelList } from "@/shared/utils/models";

const deepseek = new Hono();

const deepseekProxy = createProxy({
  provider: "deepseek",
  basePath: "https://api.deepseek.com",
  validateSchema: getDeepSeekSchema,
});

export async function generateDeepseekModelList(ctx: Context) {
  const provider = ctx.var.keyPool.getKeyProvider("deepseek");
  const avaiable = await provider.available();

  if (avaiable === 0) return [];

  const keys = await provider.list();
  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));

  return createModelList(modelIds, "deepseek");
}

deepseek.get("/v1/models", async (ctx) => {
  const models = await generateDeepseekModelList(ctx);
  return Response.json({ object: "list", data: models });
});

deepseek.post("/v1/chat/completions", deepseekProxy);

export { deepseek };
