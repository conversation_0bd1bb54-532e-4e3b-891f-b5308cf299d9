import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Icons } from "@/components/ui/icons";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

import { useTRPC } from "@/lib/trpc/client";
import { LLM_PROVIDERS, LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";

const addKeySchema = z.object({
  provider: z.enum(LLM_PROVIDERS),
  keys: z.string().min(1, "Keys are required"),
});

type AddKeyForm = z.infer<typeof addKeySchema>;

type AddKeyDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function AddKeyDialog({ open, onOpenChange }: AddKeyDialogProps) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<AddKeyForm>({
    resolver: zodResolver(addKeySchema),
    defaultValues: {
      keys: "",
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const selectedProvider = watch("provider");

  const { mutateAsync: addKeys } = useMutation(trpc.keys.addKeys.mutationOptions());

  const onSubmit = async (data: AddKeyForm) => {
    setIsSubmitting(true);
    try {
      const result = await addKeys(data);

      // Show success message
      toast.success(result.message);

      // Invalidate keys query to refresh the list
      queryClient.invalidateQueries({ queryKey: ["keys"] });

      // Reset form and close dialog
      reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to add keys:", error);
      toast.error(
        `Failed to add keys: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  // Count the number of keys in the textarea
  const keyCount =
    watch("keys")
      ?.split(",")
      .map((key) => key.trim())
      .filter((key) => key.length > 0).length || 0;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Add API Keys</DialogTitle>
          <DialogDescription>
            Add new API keys to the system. Keys should be separated by commas.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Provider Selection */}
          <div className="space-y-2">
            <Label htmlFor="provider">Provider *</Label>
            <Select
              value={selectedProvider}
              onValueChange={(value) => setValue("provider", value as LLM_Providers)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a provider" />
              </SelectTrigger>
              <SelectContent>
                {LLM_PROVIDERS.map((provider) => (
                  <SelectItem key={provider} value={provider}>
                    <div className="flex items-center gap-2">
                      <Icons.provider provider={provider} className="size-4" />
                      <span>{LLM_PROVIDER_DISPLAY_NAME[provider]}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.provider && <p className="text-sm text-red-500">{errors.provider.message}</p>}
          </div>

          {/* Keys Input */}
          <div className="space-y-2">
            <Label htmlFor="keys">
              API Keys *{" "}
              {keyCount > 0 && <span className="text-muted-foreground">({keyCount} keys)</span>}
            </Label>
            <Textarea
              id="keys"
              placeholder={
                selectedProvider
                  ? "Paste your API keys here, separated by commas..."
                  : "Select a provider first"
              }
              disabled={!selectedProvider}
              rows={4}
              {...register("keys")}
              className={errors.keys ? "border-red-500" : ""}
            />
            {errors.keys && <p className="text-sm text-red-500">{errors.keys.message}</p>}
            <p className="text-muted-foreground text-xs">
              Separate multiple keys with commas. Keys will be automatically checked after adding.
            </p>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || !selectedProvider}>
              {isSubmitting ? "Adding..." : "Add Keys"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
