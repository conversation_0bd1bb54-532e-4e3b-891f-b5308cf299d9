import { sql } from "drizzle-orm";
import { integer, text } from "drizzle-orm/sqlite-core";

import { nanoid } from "nanoid";

import { LLM_PROVIDERS } from "@/shared/providers";

import { sqliteTable } from "../helpers";

import { keys } from "./keys";
import { modelFamilies } from "./modelFamilies";
import { users } from "./users";

export const logs = sqliteTable("logs", {
  id: text()
    .primaryKey()
    .$defaultFn(() => nanoid()),

  provider: text({ enum: LLM_PROVIDERS }).notNull(),
  model: text().notNull(),

  modelFamilyId: text()
    .notNull()
    .references(() => modelFamilies.id, { onDelete: "cascade", onUpdate: "cascade" }),
  keyHash: text()
    .notNull()
    .references(() => keys.hash, { onDelete: "cascade", onUpdate: "cascade" }),
  userId: text()
    .notNull()
    .references(() => users.id, { onDelete: "cascade", onUpdate: "cascade" }),

  inputTokens: integer().notNull().default(0),
  outputTokens: integer().notNull().default(0),
  reasoningTokens: integer().notNull().default(0),

  createdAt: integer()
    .default(sql`(strftime('%s', 'now'))`)
    .notNull(),
});
