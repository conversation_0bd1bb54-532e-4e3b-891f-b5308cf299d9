import type { Context } from "hono";
import { z } from "zod";

import { config } from "@/config";

import { withinRange } from "@/shared/utils";

import { getOpenAISchema } from "./input-openai-chat";

const TextPartSchema = z.object({
  text: z.string().optional(),
  thought: z.boolean().optional(),
  inlineData: z
    .union([
      z.object({ mime_type: z.string(), data: z.string() }),
      z.object({ mimeType: z.string(), data: z.string() }),
    ])
    .optional(),

  functionCall: z.object({ name: z.string(), args: z.record(z.string(), z.unknown()) }).optional(),
  functionResponse: z
    .object({
      id: z.string().optional(),
      name: z.string(),
      response: z.object({ name: z.string(), content: z.record(z.string(), z.unknown()) }),
    })
    .optional(),
});

const GoogleAIV1ContentSchema = z.object({
  parts: z.union([z.array(TextPartSchema), TextPartSchema]),
  role: z.enum(["user", "model", "system"]).optional(),
});

const SafetySettingsSchema = z
  .array(
    z.object({
      category: z.enum([
        "HARM_CATEGORY_HARASSMENT",
        "HARM_CATEGORY_HATE_SPEECH",
        "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "HARM_CATEGORY_DANGEROUS_CONTENT",
        "HARM_CATEGORY_CIVIC_INTEGRITY",
        "HARM_CATEGORY_UNSPECIFIED",
      ]),
      threshold: z.enum([
        "OFF",
        "BLOCK_NONE",
        "BLOCK_ONLY_HIGH",
        "BLOCK_MEDIUM_AND_ABOVE",
        "BLOCK_LOW_AND_ABOVE",
        "HARM_BLOCK_THRESHOLD_UNSPECIFIED",
      ]),
    }),
  )
  .optional();

const GoogleToolSchema = z.object({
  functionDeclarations: z
    .array(
      z.object({
        name: z.string(),
        description: z.string(),
        parameters: z.record(z.string(), z.unknown()).optional(),
        response: z.record(z.string(), z.unknown()).optional(),
      }),
    )
    .optional(),

  googleSearchRetrieval: z
    .object({
      dynamicRetrievalConfig: z.object({
        mode: z.enum(["MODE_UNSPECIFIED", "MODE_DYNAMIC"]),
        dynamicThreshold: z.number(),
      }),
    })
    .optional(),

  codeExecution: z.object({}).optional(),
  googleSearch: z.object({}).optional(),
});

// https://developers.generativeai.google/api/rest/generativelanguage/models/generateContent
export const GeminiGenerateContentSchema = z.object({
  model: z.string().optional(),
  contents: z.array(GoogleAIV1ContentSchema),

  tools: z.union([GoogleToolSchema, GoogleToolSchema.array()]).optional(),
  toolConfig: z
    .object({
      functionCallingConfig: z.object({
        mode: z.enum(["MODE_UNSPECIFIED", "AUTO", "ANY", "NONE"]).optional(),
        allowedFunctionNames: z.array(z.string()).optional(),
      }),
    })
    .optional(),

  safetySettings: SafetySettingsSchema,
  systemInstruction: z.union([GoogleAIV1ContentSchema, GoogleAIV1ContentSchema.array()]).optional(),
  // quick fix for SillyTavern, which uses camel case field names for everything
  // except for system_instruction where it randomly uses snake case.
  // google api evidently accepts either case.
  system_instruction: z
    .union([GoogleAIV1ContentSchema, GoogleAIV1ContentSchema.array()])
    .optional(),

  generationConfig: z
    .object({
      stopSequences: z.array(z.string()).optional(),

      responseMimeType: z.string().optional(),
      responseSchema: z
        .object({
          required: z.array(z.string()).optional(),
          type: z.string().optional(),
          properties: z.record(z.string(), z.unknown()).optional(),
        })
        .optional(),

      responseModalities: z
        .array(z.enum(["MODALITY_UNSPECIFIED", "TEXT", "IMAGE", "AUDIO"]))
        .optional(),

      candidateCount: z.literal(1).optional(),
      maxOutputTokens: z.coerce.number().int().optional(),
      temperature: z.number().min(0).max(2).optional(),
      topP: z.number().min(0).max(1).optional(),
      topK: z.number().int().min(1).max(999).optional(),
      seed: z.number().int().optional(),
      presencePenalty: z.number().optional(),
      frequencyPenalty: z.number().optional(),

      responseLogprobs: z.boolean().optional(),
      logprobs: z.number().int().optional(),
      enableEnhancedCivicAnswers: z.boolean().optional(),

      speechConfig: z
        .object({
          voiceConfig: z.object({ prebuiltVoiceConfig: z.object({ voiceName: z.string() }) }),
          languageCode: z.string(),
        })
        .optional(),

      thinkingConfig: z
        .object({
          includeThoughts: z.boolean().optional(),
          // https://ai.google.dev/gemini-api/docs/thinking
          // -1     - Use the dynamic thinking (For all models)
          // 0      - Disable thinking on Gemini 2.5 Flash and Flash lite. Gemini 2.5 Pro are not allowed to disable thinking.
          // 128    - Minimum thinking budget on Gemini 2.5 Pro
          // 512    - Minimum thinking budget on Gemini 2.5 Flash Lite
          // 24_576 - Maximum thinking budget on Gemini 2.5 Flash and Flash lite
          // 32_768 - Maximum thinking budget on Gemini 2.5 Pro
          thinkingBudget: z.number().int().min(-1).max(32_768).optional(),
        })
        .optional(),

      mediaResolution: z
        .enum([
          "MEDIA_RESOLUTION_UNSPECIFIED",
          "MEDIA_RESOLUTION_LOW",
          "MEDIA_RESOLUTION_MEDIUM",
          "MEDIA_RESOLUTION_HIGH",
        ])
        .optional(),
    })
    .optional(),
});

export type GoogleAIChatMessage = z.infer<typeof GeminiGenerateContentSchema>["contents"][0];

function getThinkingBudget(model: string, thinkingBudget: number) {
  // Dynamic thinking
  if (thinkingBudget === -1) return thinkingBudget;

  if (model.includes("gemini-2.5-pro")) {
    return withinRange({ input: thinkingBudget, min: 128, max: 32_768 });
  }

  // Disable thinking
  if (thinkingBudget === 0) return 0;

  if (model.includes("gemini-2.5-flash-lite")) {
    return withinRange({ input: thinkingBudget, min: 512, max: 24_576 });
  }
  return withinRange({ input: thinkingBudget, min: 0, max: 24_576 });
}

export function getGeminiSchema(ctx: Context) {
  const model = ctx.var.requestData!.model;

  return GeminiGenerateContentSchema.transform((data) => {
    // const maxOutput =
    //   config.modelFamilySettings.get(modelFamily)?.maxOutput ?? config.defaultGlobalMaxOutput;
    // const modelMaxOutput = getModelMaxOutputLimit(ctx);

    if (!config.allowToolUsage.includes(ctx.var.service)) {
      delete data.tools;
      delete data.toolConfig;
    }

    /**
     * This is a hot fix for Gemini 2.0 Flash Experimental, copy-pasted from
     * SillyTavern. All right reserved to SillyTavern.
     *
     * @see https://github.com/SillyTavern/SillyTavern/issues/3267
     * @see https://github.com/SillyTavern/SillyTavern/commit/2103e6238cbdf08232147edb165c26c32725dc02
     */
    if (model.includes("gemini-2.0-flash-exp")) {
      data.safetySettings = data.safetySettings?.map((setting) => ({
        ...setting,
        threshold: "OFF",
      }));
    }

    if (model.includes("gemini-2.5")) {
      // Gemini 2.5 family does not support presence and frequency penalty
      delete data.generationConfig?.presencePenalty;
      delete data.generationConfig?.frequencyPenalty;

      if (data.generationConfig?.thinkingConfig?.thinkingBudget) {
        const budget = data.generationConfig.thinkingConfig.thinkingBudget;
        data.generationConfig.thinkingConfig.thinkingBudget = getThinkingBudget(model, budget);
      }
    }

    // data.generationConfig.maxOutputTokens = Math.min(
    //   data.generationConfig.maxOutputTokens ?? Number.MAX_SAFE_INTEGER,
    //   modelMaxOutput,
    //   maxOutput,
    // );

    return data;
  });
}

export function transformOpenAIToGemini(ctx: Context) {
  const body = ctx.req.bodyCache.json;
  const result = getOpenAISchema(ctx).safeParse(body);

  if (!result.success) {
    ctx.var.logger.warn({ issues: result.error.issues, body }, "Invalid OpenAI-to-Gemini request");
    throw result.error;
  }

  const { messages, ...rest } = result.data;

  const contents: GoogleAIChatMessage[] = messages
    .filter((m) => ["assistant", "user"].includes(m.role))
    .map((m) => {
      const role = m.role === "assistant" ? ("model" as const) : ("user" as const);
      if (typeof m.content === "string") return { role, parts: [{ text: m.content }] };

      return {
        role,
        parts: m.content.map((content) => {
          if (content.type === "text") return { text: content.text };

          // base64 header: data:image/(png|jpeg|...);base64,
          // Which we will extract the mime type from or default to png if there is no header
          // We also strip the header if it exists before send to google since google throw error when it included
          const mime_type = content.image_url.url.split(";")[0]?.split(":")[1] ?? "image/png";
          const imageData = content.image_url.url.replace(/^data:image\/.*;base64,/, "");

          return { inlineData: { mime_type, data: imageData } };
        }),
      };
    });

  const systemMessages = messages.filter((m) => ["system", "developer"].includes(m.role));

  const systemInstruction: z.infer<
    (typeof GeminiGenerateContentSchema)["shape"]["systemInstruction"]
  > =
    systemMessages.length === 0
      ? undefined
      : {
          parts: systemMessages.map((m) => {
            if (typeof m.content === "string") return { text: m.content };
            return { text: (m.content as unknown as { type: "text"; text: string }).text };
          }),
        };

  const stops = Array.from(
    new Set(rest.stop ? (Array.isArray(rest.stop) ? rest.stop : [rest.stop]) : []),
  );

  const model = String(body.model);
  let safetySettings = [
    { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
    { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
    { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
    { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
    { category: "HARM_CATEGORY_CIVIC_INTEGRITY", threshold: "BLOCK_NONE" },
  ] as z.infer<typeof SafetySettingsSchema>;

  // Use max_completion_tokens/max_tokens from the body because the result is after transform
  // and it could be different from the original request.
  const maxTokens = Math.min(
    body.max_completion_tokens ?? body.max_tokens ?? Number.MAX_SAFE_INTEGER,
  );

  const effortToBudget = { low: 1024, medium: 4096, high: 16384 };
  const thinkingBudget = body.reasoning_effort
    ? // Thinking budget should not exceed 75% of the max tokens.
      Math.min(
        effortToBudget[body.reasoning_effort as keyof typeof effortToBudget],
        maxTokens * 0.75,
      )
    : undefined;

  const googleBody: z.infer<typeof GeminiGenerateContentSchema> = {
    model: model,
    contents,
    systemInstruction,
    generationConfig: {
      maxOutputTokens: maxTokens,
      stopSequences: stops,
      topP: body.top_p,
      topK: body.topK ?? 40,
      temperature: body.temperature,
      presencePenalty: body.presence_penalty,
      frequencyPenalty: body.frequency_penalty,
      seed: body.seed,
      thinkingConfig: { thinkingBudget },
    },
    safetySettings,
  };

  return getGeminiSchema(ctx).parse(googleBody);
}
