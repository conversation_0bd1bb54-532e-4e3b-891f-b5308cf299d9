import pino from "pino";

import { config } from "./config";

export const baseConfig: pino.LoggerOptions = {
  level: config.logging.level,
  redact: {
    paths: [
      "request.headers.host",
      "request.headers.cookie",
      "request.headers.authorization",
      'request.headers["api-key"]',
      'request.headers["x-api-key"]',
      'request.headers["x-real-ip"]',
      'resuest.headers["set-cookie"]',
      'request.headers["x-goog-api-key"]',
      'request.headers["x-forwarded-for"]',
      'request.headers["cf-connecting-ip"]',
      'request.headers["cf-ipcountry"]',
    ],
    censor: "********",
  },

  base: { pid: process.pid, module: "server" },
  transport: {
    target: "pino-pretty",
    options: {
      translateTime: "dd/mm/yy - HH:MM:ss",
      messageFormat: "{if module}\x1b[90m[{module}] \x1b[39m{end}{msg}",
      ignore: "module",
      colorize: true,
      singleLine: true,
    },
  },
};

export const logger = pino(baseConfig);
