import { useState, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { MoreHorizontalIcon, PowerIcon, PowerOffIcon, TrashIcon, UndoIcon } from "lucide-react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

import { useTRPC } from "@/lib/trpc/client";
import type { Key } from "@/shared/key-management";

type ActionType = "disable" | "enable" | "delete" | "cancelDeletion" | null;

interface KeyActionsProps {
  keyData: Key;
  refetch?: () => void;
}

export function KeyActions({ keyData, refetch }: KeyActionsProps) {
  const [actionType, setActionType] = useState<ActionType>(null);
  const [disableReason, setDisableReason] = useState("");

  const trpc = useTRPC();
  const queryClient = useQueryClient();

  // Get pending deletion time from metadata
  const pendingDeletion = keyData.metadata?.pendingDeletion as number | undefined;
  const isPendingDeletion = pendingDeletion && pendingDeletion > Date.now() / 1000;

  const isDisabled = keyData.status === "disabled";
  const isRevoked = keyData.status === "revoked";

  // Mutations using TRPC mutations
  const { mutate: disableKey, isPending: isDisabling } = useMutation({
    ...trpc.keys.disable.mutationOptions(),
    onSuccess: () => {
      toast.success("Key disabled successfully");
      refetch?.();
      setActionType(null);
      setDisableReason("");
    },
    onError: (error) => {
      toast.error(`Failed to disable key: ${error.message}`);
    },
  });

  const { mutate: enableKey, isPending: isEnabling } = useMutation({
    ...trpc.keys.enable.mutationOptions(),
    onSuccess: () => {
      toast.success("Key enabled successfully");
      refetch?.();
      setActionType(null);
    },
    onError: (error) => {
      toast.error(`Failed to enable key: ${error.message}`);
    },
  });

  const { mutate: markKeyForDeletion, isPending: isMarkingForDeletion } = useMutation({
    ...trpc.keys.markForDeletion.mutationOptions(),
    onSuccess: () => {
      toast.success("Key marked for deletion (will be deleted in 24 hours)");
      refetch?.();
      setActionType(null);
    },
    onError: (error) => {
      toast.error(`Failed to mark key for deletion: ${error.message}`);
    },
  });

  const { mutate: cancelKeyDeletion, isPending: isCancellingDeletion } = useMutation({
    ...trpc.keys.cancelDeletion.mutationOptions(),
    onSuccess: () => {
      toast.success("Key deletion cancelled");
      refetch?.();
      setActionType(null);
    },
    onError: (error) => {
      toast.error(`Failed to cancel deletion: ${error.message}`);
    },
  });

  // Event handlers
  const handleDisable = useCallback(() => {
    setActionType("disable");
  }, []);

  const handleEnable = useCallback(() => {
    setActionType("enable");
  }, []);

  const handleDelete = useCallback(() => {
    setActionType("delete");
  }, []);

  const handleCancelDeletion = useCallback(() => {
    setActionType("cancelDeletion");
  }, []);

  const handleConfirmDisable = useCallback(() => {
    if (!disableReason.trim()) {
      toast.error("Please provide a reason for disabling the key");
      return;
    }
    disableKey({ hash: keyData.hash, reason: disableReason.trim() });
  }, [disableReason, keyData.hash, disableKey]);

  const handleConfirmEnable = useCallback(() => {
    enableKey({ hash: keyData.hash });
  }, [keyData.hash, enableKey]);

  const handleConfirmDelete = useCallback(() => {
    markKeyForDeletion({ hash: keyData.hash });
  }, [keyData.hash, markKeyForDeletion]);

  const handleConfirmCancelDeletion = useCallback(() => {
    cancelKeyDeletion({ hash: keyData.hash });
  }, [keyData.hash, cancelKeyDeletion]);

  const closeDialog = useCallback(() => {
    setActionType(null);
    setDisableReason("");
  }, []);

  return (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreHorizontalIcon className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end">
          {isRevoked ? (
            // Revoked keys can only be deleted
            <DropdownMenuItem onClick={handleDelete} className="text-red-600">
              <TrashIcon className="mr-2 h-4 w-4" />
              Delete Key
            </DropdownMenuItem>
          ) : !isDisabled ? (
            // Working/active keys can only be disabled
            <DropdownMenuItem onClick={handleDisable} className="text-orange-600">
              <PowerOffIcon className="mr-2 h-4 w-4" />
              Disable Key
            </DropdownMenuItem>
          ) : (
            // Disabled keys can be enabled or deleted
            <>
              <DropdownMenuItem onClick={handleEnable} className="text-green-600">
                <PowerIcon className="mr-2 h-4 w-4" />
                Enable Key
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {!isPendingDeletion ? (
                <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                  <TrashIcon className="mr-2 h-4 w-4" />
                  Delete Key
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={handleCancelDeletion} className="text-blue-600">
                  <UndoIcon className="mr-2 h-4 w-4" />
                  Cancel Deletion
                </DropdownMenuItem>
              )}
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Disable Key Dialog */}
      <AlertDialog open={actionType === "disable"} onOpenChange={closeDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Disable API Key</AlertDialogTitle>
            <AlertDialogDescription>
              This will disable the key and prevent it from being used. You can re-enable it later.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="disable-reason">Reason for disabling</Label>
              <Textarea
                id="disable-reason"
                placeholder="Enter the reason for disabling this key..."
                value={disableReason}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  setDisableReason(e.target.value)
                }
                className="mt-1"
              />
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDisable}
              disabled={isDisabling || !disableReason.trim()}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isDisabling ? "Disabling..." : "Disable Key"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Enable Key Dialog */}
      <AlertDialog open={actionType === "enable"} onOpenChange={closeDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Enable API Key</AlertDialogTitle>
            <AlertDialogDescription>
              This will re-enable the key and allow it to be used again. The key status will be
              rechecked.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmEnable}
              disabled={isEnabling}
              className="bg-green-600 hover:bg-green-700"
            >
              {isEnabling ? "Enabling..." : "Enable Key"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Key Dialog */}
      <AlertDialog open={actionType === "delete"} onOpenChange={closeDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete API Key</AlertDialogTitle>
            <AlertDialogDescription>
              {isRevoked
                ? "This revoked key will be marked for deletion and permanently deleted in 24 hours. You can cancel the deletion before then."
                : "This will mark the key for deletion. The key will be permanently deleted in 24 hours. You can cancel the deletion before then."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={isMarkingForDeletion}
              className="bg-red-600 hover:bg-red-700"
            >
              {isMarkingForDeletion ? "Marking for deletion..." : "Delete Key"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Cancel Deletion Dialog */}
      <AlertDialog open={actionType === "cancelDeletion"} onOpenChange={closeDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Key Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              This will cancel the scheduled deletion of this key. The key will remain disabled.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmCancelDeletion}
              disabled={isCancellingDeletion}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isCancellingDeletion ? "Cancelling..." : "Cancel Deletion"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
