import { SelectIcon } from "@radix-ui/react-select";
import type { Column, SortDirection } from "@tanstack/react-table";
import { ArrowDown, ArrowUp, Check, ChevronsUpDown, EyeOff } from "lucide-react";
import { useCallback } from "react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { cn } from "@/shared/utils";

interface DataTableColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>;
  title: string;
}

export function DataTableColumnHeader<TData, TValue>({
  column,
  title,
  className,
}: DataTableColumnHeaderProps<TData, TValue>) {
  "use no memo";

  const handleSorting = useCallback(
    (direction: SortDirection) => column.toggleSorting(direction === "desc"),
    [column],
  );

  if (!column.getCanSort() && !column.getCanHide()) {
    return <div className={className}>{title}</div>;
  }

  return (
    <div className={cn("flex h-full items-center gap-2", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger
          aria-label={
            column.getIsSorted() === "desc"
              ? "Sorted descending. Click to sort ascending."
              : column.getIsSorted() === "asc"
                ? "Sorted ascending. Click to sort descending."
                : "Not sorted. Click to sort ascending."
          }
          className="hover:bg-background/70 data-[state=open]:hover:bg-background/70 flex h-8 w-fit items-center rounded-md px-2 text-xs"
        >
          <span className="w-max">{title}</span>
          <SelectIcon asChild>
            {column.getCanSort() && column.getIsSorted() === "desc" ? (
              <ArrowDown className="ml-2.5 size-4" aria-hidden="true" />
            ) : column.getIsSorted() === "asc" ? (
              <ArrowUp className="ml-2.5 size-4" aria-hidden="true" />
            ) : (
              <ChevronsUpDown className="ml-2.5 size-4" aria-hidden="true" />
            )}
          </SelectIcon>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="start">
          {column.getCanSort() && (
            <>
              <DataTableMenuItem onMouseDown={() => handleSorting("asc")}>
                <span className="flex items-center">
                  <ArrowUp className="mr-2 size-3.5" aria-hidden="true" />
                  Asc
                </span>

                {column.getIsSorted() === "asc" && <Check className="size-3.5" />}
              </DataTableMenuItem>

              <DataTableMenuItem onMouseDown={() => handleSorting("desc")}>
                <span className="flex items-center">
                  <ArrowDown className="mr-2 size-3.5" aria-hidden="true" />
                  Desc
                </span>

                {column.getIsSorted() === "desc" && <Check className="size-3.5" />}
              </DataTableMenuItem>
            </>
          )}

          {column.getCanHide() && (
            <DataTableMenuItem onMouseDown={() => column.toggleVisibility(false)}>
              <span className="flex items-center">
                <EyeOff className="mr-2 size-3.5" aria-hidden="true" />
                Hide
              </span>
            </DataTableMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

function DataTableMenuItem({
  children,
  className,
  ...props
}: React.ComponentProps<typeof DropdownMenuItem>) {
  return (
    <DropdownMenuItem
      className={cn("w-full justify-between focus:cursor-pointer", className)}
      {...props}
    >
      {children}
    </DropdownMenuItem>
  );
}
DataTableMenuItem.displayName = "DataTableMenuItem";
