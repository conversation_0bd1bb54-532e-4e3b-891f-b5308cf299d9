import type pino from "pino";

import type { Key } from "./shared/key-management";
import type { KeysPool } from "./shared/key-management/keys-pool";
import type { LLM_Providers } from "./shared/providers";
import type { db } from "./shared/database";

declare module "hono" {
  interface ContextVariableMap {
    token: string;
    logger: pino.Logger;
    keyPool: KeysPool;
    db: typeof db;

    requestData?: {
      model: string;
      streaming: boolean;
      provider: LLM_Providers;

      selectedKey?: Key;
      familyId: string | null;
    };
  }
}
