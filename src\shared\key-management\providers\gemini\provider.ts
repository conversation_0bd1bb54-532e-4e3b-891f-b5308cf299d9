import { type Key } from "@/shared/key-management";
import { BaseKeyProvider } from "@/shared/key-management/base-provider";

import { config } from "@/config";

import { <PERSON><PERSON>ey<PERSON>he<PERSON> } from "./checker";

export interface Gemini<PERSON>ey extends Key {
  readonly provider: "gemini";

  metadata: {
    tier: "free" | "paid" | "unknown";
  };
}

export class GeminiKeyProvider extends BaseKeyProvider<GeminiKey> {
  static override readonly provider = "gemini" as const;

  protected override getProviderPrefix(): string {
    return "gem";
  }

  constructor() {
    super(GeminiKeyProvider.provider);
  }

  public override async init(): Promise<void> {
    const checker = new GeminiKeyChecker({ updateFn: this.update.bind(this) });
    await super.init({ checker });
  }

  protected getSeedKeysFromConfig(): string[] {
    return config.keys.gemini;
  }

  protected override serializeNewKeyMetadataForInsert(): <PERSON><PERSON><PERSON>["metadata"] {
    return { tier: "unknown" } satisfies <PERSON><PERSON><PERSON>["metadata"];
  }

  protected override matchesModel(k: <PERSON><PERSON><PERSON>, modelId: string): boolean {
    if (!k.modelIds || (k.modelIds.length > 0 && !k.modelIds.includes(modelId))) return false;
    return true;
  }

  protected override buildUsageStatsModelFamily(modelId: string): string {
    return modelId;
  }
}
