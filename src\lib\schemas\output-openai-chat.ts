import { z } from "zod";

export const OpenAIUsageSchema = z.object({
  prompt_tokens: z.number().int(),
  completion_tokens: z.number().int(),
  total_tokens: z.number().int(),
  completion_tokens_details: z.object({ reasoning_tokens: z.number().int() }).optional(),
});

export const OpenAIChatCompletionResponseSchema = z.object({
  id: z.string(),
  object: z.literal("chat.completion"),
  created: z.number().int(),
  model: z.string(),
  choices: z.array(
    z.object({
      index: z.number().int(),
      message: z.object({
        role: z.enum(["assistant", "tool", "function"]),
        content: z.string().nullish(),
        refusal: z.string().nullish(),
        annotations: z.array(z.any()).optional(),
        tool_calls: z.array(z.record(z.string(), z.unknown())).optional(),
      }),
      finish_reason: z.string(),
    }),
  ),
  usage: OpenAIUsageSchema.nullish(),
});

export type OpenAIChatCompletionResponse = z.infer<typeof OpenAIChatCompletionResponseSchema>;

export const OpenAIChatCompletionStreamResponseSchema = z.object({
  id: z.string(),
  object: z.literal("chat.completion.chunk"),
  created: z.number().int(),
  model: z.string(),

  choices: z
    .object({
      index: z.number().int(),
      delta: z.object({
        role: z.enum(["assistant", "tool", "function"]).optional(),
        content: z.string().optional(),

        logprobs: z.record(z.string(), z.unknown()).nullable(),
        tool_calls: z.array(z.record(z.string(), z.unknown())).optional(),
      }),
      finish_reason: z.string().nullish(),
    })
    .array(),

  usage: OpenAIUsageSchema.nullish(),
  obfuscation: z.string().nullish(),
});

export type OpenAIChatCompletionStreamResponse = z.infer<
  typeof OpenAIChatCompletionStreamResponseSchema
>;
