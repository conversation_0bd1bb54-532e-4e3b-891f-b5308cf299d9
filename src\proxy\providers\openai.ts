import { Hono, type Context } from "hono";

import { createProxy } from "@/lib/middlewares/create-proxy";
import { getOpenAISchema } from "@/lib/schemas/input-openai-chat";

import { createModelList } from "@/shared/utils/models";

const openai = new Hono();

const openaiProxy = createProxy({
  provider: "openai",
  basePath: "https://api.openai.com",
  validateSchema: getOpenAISchema,
});

export async function generateDeepseekModelList(ctx: Context) {
  const provider = ctx.var.keyPool.getKeyProvider("openai");
  const avaiable = await provider.available();

  if (avaiable === 0) return [];

  const keys = await provider.list();
  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));

  return createModelList(modelIds, "openai");
}

openai.get("/v1/models", async (ctx) => {
  const models = await generateDeepseekModelList(ctx);
  return Response.json({ object: "list", data: models });
});

openai.post("/v1/chat/completions", openaiProxy);

export { openai };
