import { sql } from "drizzle-orm";
import * as schedule from "node-schedule";

import { db, schema } from "@/shared/database";
import { logger } from "@/logger";

/**
 * Cleanup job that runs every hour to delete keys that have been marked for deletion
 * and whose deletion time has passed.
 */
export class KeyCleanupService {
  private static instance: KeyCleanupService;
  private cleanupJob?: schedule.Job;
  private readonly logger = logger.child({ module: "key-cleanup" });

  private constructor() {}

  public static getInstance(): KeyCleanupService {
    if (!KeyCleanupService.instance) {
      KeyCleanupService.instance = new KeyCleanupService();
    }
    return KeyCleanupService.instance;
  }

  /**
   * Start the cleanup service
   */
  public start(): void {
    if (this.cleanupJob) {
      this.logger.warn("Cleanup service is already running");
      return;
    }

    // Run every hour at minute 0
    this.cleanupJob = schedule.scheduleJob("0 * * * *", async () => {
      await this.performCleanup();
    });

    this.logger.info("Key cleanup service started - will run every hour");

    // Also run immediately on startup
    this.performCleanup().catch((error) => {
      this.logger.error({ error }, "Failed to perform initial cleanup");
    });
  }

  /**
   * Stop the cleanup service
   */
  public stop(): void {
    if (this.cleanupJob) {
      this.cleanupJob.cancel();
      this.cleanupJob = undefined;
      this.logger.info("Key cleanup service stopped");
    }
  }

  /**
   * Perform the actual cleanup of expired keys
   */
  private async performCleanup(): Promise<void> {
    try {
      const now = Math.floor(Date.now() / 1000);

      // Find keys that are marked for deletion and whose deletion time has passed
      const keysToDelete = await db.query.keys.findMany({
        where: sql`json_extract(metadata, '$.pendingDeletion') IS NOT NULL 
                   AND json_extract(metadata, '$.pendingDeletion') <= ${now}`,
      });

      if (keysToDelete.length === 0) {
        this.logger.debug("No keys found for cleanup");
        return;
      }

      this.logger.info(
        { count: keysToDelete.length },
        "Found keys marked for deletion that have expired"
      );

      // Delete the expired keys
      for (const key of keysToDelete) {
        try {
          await db.delete(schema.keys).where(sql`hash = ${key.hash}`);
          
          this.logger.info(
            { 
              hash: key.hash.slice(0, 8) + "...", 
              provider: key.provider,
              deletionTime: key.metadata?.pendingDeletion 
            },
            "Deleted expired key"
          );
        } catch (error) {
          this.logger.error(
            { 
              error, 
              hash: key.hash.slice(0, 8) + "...", 
              provider: key.provider 
            },
            "Failed to delete expired key"
          );
        }
      }

      this.logger.info(
        { deletedCount: keysToDelete.length },
        "Cleanup completed successfully"
      );
    } catch (error) {
      this.logger.error({ error }, "Failed to perform key cleanup");
    }
  }

  /**
   * Manually trigger cleanup (useful for testing)
   */
  public async triggerCleanup(): Promise<void> {
    this.logger.info("Manually triggering key cleanup");
    await this.performCleanup();
  }
}

// Export singleton instance
export const keyCleanupService = KeyCleanupService.getInstance();
