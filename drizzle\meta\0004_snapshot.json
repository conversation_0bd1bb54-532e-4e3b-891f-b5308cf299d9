{"version": "6", "dialect": "sqlite", "id": "3b9282eb-e366-4d14-acaf-88a5d412b0f8", "prevId": "b31dc0b6-1c80-4db6-b0ee-15fa8efe7e4f", "tables": {"proxy_key_stats": {"name": "proxy_key_stats", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "hash": {"name": "hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelFamily": {"name": "modelFamily", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "inputTokens": {"name": "inputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputTokens": {"name": "outputTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "reasoningTokens": {"name": "reasoningTokens", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "prompts": {"name": "prompts", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"idx_key_stats_hash_model_family": {"name": "idx_key_stats_hash_model_family", "columns": ["hash", "modelFamily"], "isUnique": true}}, "foreignKeys": {"proxy_key_stats_hash_proxy_keys_hash_fk": {"name": "proxy_key_stats_hash_proxy_keys_hash_fk", "tableFrom": "proxy_key_stats", "tableTo": "proxy_keys", "columnsFrom": ["hash"], "columnsTo": ["hash"], "onDelete": "cascade", "onUpdate": "cascade"}, "proxy_key_stats_modelFamily_proxy_modelFamilies_id_fk": {"name": "proxy_key_stats_modelFamily_proxy_modelFamilies_id_fk", "tableFrom": "proxy_key_stats", "tableTo": "proxy_modelFamilies", "columnsFrom": ["modelFamily"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "proxy_keys": {"name": "proxy_keys", "columns": {"hash": {"name": "hash", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "encryptedKey": {"name": "encrypted<PERSON>ey", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "modelIds": {"name": "modelIds", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'unknown'"}, "disabledAt": {"name": "disabledAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "disabledBy": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ratelimitedAt": {"name": "ratelimitedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "ratelimitedUntil": {"name": "ratelimitedUntil", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "lastUsedAt": {"name": "lastUsedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "lastCheckedAt": {"name": "lastCheckedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "lastModelsCheckedAt": {"name": "lastModelsCheckedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "nextCheckAt": {"name": "nextCheckAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'{}'"}}, "indexes": {"idx_keys_provider": {"name": "idx_keys_provider", "columns": ["hash", "provider"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "proxy_modelFamilies": {"name": "proxy_modelFamilies", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "models": {"name": "models", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'disabled'"}, "inputCost": {"name": "inputCost", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "outputCost": {"name": "outputCost", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "maxContext": {"name": "max<PERSON><PERSON><PERSON>t", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "maxOutput": {"name": "maxOutput", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "ratelimitCost": {"name": "ratelimitCost", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {"proxy_modelFamilies_name_unique": {"name": "proxy_modelFamilies_name_unique", "columns": ["name"], "isUnique": true}, "idx_models_provider": {"name": "idx_models_provider", "columns": ["provider"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "proxy_users": {"name": "proxy_users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"proxy_users_username_unique": {"name": "proxy_users_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {"\"proxy_modelFamilies\".\"weight\"": "\"proxy_modelFamilies\".\"ratelimitCost\""}}, "internal": {"indexes": {}}}