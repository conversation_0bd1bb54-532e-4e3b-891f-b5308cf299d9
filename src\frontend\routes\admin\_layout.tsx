import { useMutation, useQuery } from "@tanstack/react-query";
import {
  BoxesIcon,
  KeySquareIcon,
  LayoutGridIcon,
  LogOutIcon,
  UserPlusIcon,
  UsersIcon,
} from "lucide-react";
import { Link, Navigate, Outlet, useLocation } from "react-router";

import { ModeToggle } from "@/components/mode-toggle";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
} from "@/components/ui/sidebar";

import { useTRPC } from "@/lib/trpc/client";

function NavItem(props: {
  to: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}) {
  const location = useLocation();
  const isActive = location.pathname === props.to;
  return (
    <SidebarMenuItem>
      <SidebarMenuButton asChild isActive={isActive}>
        <Link to={props.to} className="flex items-center gap-2">
          <props.icon className="h-4 w-4" />
          <span>{props.label}</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

const pathToTitle: Record<string, string> = {
  "/admin/dashboard": "Dashboard",
  "/admin/users": "Users",
  "/admin/users/create": "Create user",
  "/admin/keys": "API Keys Management",
  "/admin/models": "Model Family Management",
};

function useGetPageTitle() {
  const path = useLocation();
  const title = pathToTitle[path.pathname];

  if (path.pathname.startsWith("/admin/keys/") && !title) return "API Key Details";
  return title ?? "Admin Dashboard";
}

export function AdminLayout() {
  const trpc = useTRPC();

  const { data, isPending, refetch } = useQuery(trpc.auth.isAuthenticated.queryOptions());
  const { mutateAsync: logout } = useMutation(trpc.auth.logout.mutationOptions());

  const pageTitle = useGetPageTitle();

  if (isPending) return <div>Loading...</div>;
  if (data && data.status === "unauthenticated") return <Navigate to="/admin/login" replace />;

  return (
    <SidebarProvider>
      <Sidebar className="border-r">
        <SidebarHeader>
          <div className="flex items-center gap-2 px-2 py-2 text-sm font-semibold">
            <LayoutGridIcon className="h-4 w-4" />
            <span>Admin</span>
          </div>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Users management</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <NavItem to="/admin/users" label="Users" icon={UsersIcon} />
                <NavItem to="/admin/users/create" label="Create user" icon={UserPlusIcon} />
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel>Keys management</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <NavItem to="/admin/keys" label="Keys" icon={KeySquareIcon} />
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel>Models management</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <NavItem to="/admin/models" label="Models" icon={BoxesIcon} />
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter>
          <div className="text-muted-foreground px-2 py-2 text-xs">
            <Button variant="ghost" size="sm" onClick={() => logout().then(() => refetch())}>
              <LogOutIcon className="mr-2 h-4 w-4" />
              Logout
            </Button>
          </div>
        </SidebarFooter>

        <SidebarRail />
      </Sidebar>

      <SidebarInset>
        <div className="grid h-svh w-full grid-rows-[max-content_1px_1fr]">
          <div className="flex items-center justify-between px-4 py-2">
            <div className="text-sm">{pageTitle}</div>

            <ModeToggle />
          </div>

          <Separator />

          <div className="h-full overflow-y-auto">
            <Outlet />
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
