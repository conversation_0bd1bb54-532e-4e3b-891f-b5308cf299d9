import { z } from "zod";
import { OpenAIUsageSchema } from "./output-openai-chat";

export const DeepSeekChatCompletionResponseSchema = z.object({
  id: z.string(),
  object: z.literal("chat.completion"),
  created: z.int(),
  model: z.string(),

  choices: z
    .object({
      index: z.int(),
      message: z.object({
        role: z.enum(["assistant", "tool", "function"]),
        content: z.string().nullish(),
        reasoning_content: z.string().optional(),

        function_call: z.record(z.string(), z.unknown()).optional(),
        function_response: z.record(z.string(), z.unknown()).optional(),
      }),
      logprobs: z.record(z.string(), z.unknown()).nullable(),
      finish_reason: z.string().nullish(),
    })
    .array(),

  usage: OpenAIUsageSchema.nullish(),
});

export type DeepSeekChatCompletionResponse = z.infer<typeof DeepSeekChatCompletionResponseSchema>;

export const DeepSeekChatCompletionStreamResponseSchema = z.object({
  id: z.string(),
  object: z.literal("chat.completion.chunk"),
  created: z.int(),
  model: z.string(),

  choices: z
    .object({
      index: z.int(),
      delta: z.object({
        role: z.enum(["assistant", "tool", "function"]).optional(),
        content: z.string().optional(),
      }),
      logprobs: z.record(z.string(), z.unknown()).nullable(),
      finish_reason: z.string().nullish(),
    })
    .array(),

  usage: OpenAIUsageSchema.nullish(),
});

export type DeepSeekChatCompletionStreamResponse = z.infer<
  typeof DeepSeekChatCompletionStreamResponseSchema
>;
