import type { Key } from "@/shared/key-management";
import { decrypt } from "@/shared/key-management/utils";
import type { LLM_Providers } from "@/shared/providers";

export function createHeaders(data: {
  headers: Headers;
  provider: LLM_Providers;
  key: Key;
  extra?: Record<string, string>;
}) {
  const headers = new Headers(data.headers);

  headers.delete("host");
  headers.delete("origin");
  headers.delete("referer");
  headers.delete("x-api-key");
  headers.delete("x-goog-api-key");

  // Some APIs refuse requests coming from browsers to discourage embedding
  // API keys in client-side code, so we must remove all CORS/fetch headers.
  for (const key of headers.keys()) {
    switch (true) {
      case key.startsWith("sec-"):
      case key.startsWith("x-stainless-"):
      case key.startsWith("cf-"):
        headers.delete(key);
        break;
    }
  }

  headers.delete("tailscale-user-login");
  headers.delete("tailscale-user-name");
  headers.delete("tailscale-headers-info");
  headers.delete("tailscale-user-profile-pic");
  headers.delete("forwarded");
  headers.delete("true-client-ip");
  headers.delete("x-forwarded-for");
  headers.delete("x-forwarded-host");
  headers.delete("x-forwarded-proto");
  headers.delete("x-real-ip");
  headers.delete("user-agent");

  const keyString = decrypt(data.key.encryptedKey);

  switch (data.provider) {
    case "gemini":
      headers.set("x-goog-api-key", keyString);
      break;

    default:
    case "openai":
      headers.set("Authorization", `Bearer ${keyString}`);
      if (data.key.metadata.organizationId) {
        headers.set("OpenAI-Organization", data.key.metadata.organizationId.toString());
      }

      break;
  }

  if (data.extra) {
    Object.entries(data.extra).forEach(([key, value]) => {
      headers.set(key, value);
    });
  }

  return headers;
}
