import { relations, sql } from "drizzle-orm";
import { index, integer, text, uniqueIndex } from "drizzle-orm/sqlite-core";

import { LLM_PROVIDERS } from "@/shared/providers";

import { sqliteTable } from "../helpers";
import { modelFamilies } from "./modelFamilies";

export const keys = sqliteTable(
  "keys",
  {
    hash: text().primaryKey(),
    encryptedKey: text().notNull(),

    provider: text({ enum: LLM_PROVIDERS }).notNull(),
    modelIds: text({ mode: "json" }).notNull().$type<string[]>().default([]),
    status: text({
      enum: ["working", "out_of_quota", "revoked", "ratelimited", "disabled", "unknown"],
    })
      .notNull()
      .default("unknown"),

    disabledAt: integer(),
    disabledBy: text({ enum: ["key-checker", "admin", "error"] }),

    ratelimitedAt: integer().default(0).notNull(),
    ratelimitedUntil: integer().default(0).notNull(),

    lastUsedAt: integer().default(0).notNull(),
    lastCheckedAt: integer().default(0).notNull(),
    lastModelsCheckedAt: integer().default(0).notNull(),

    nextCheckAt: integer(),

    createdAt: integer()
      .default(sql`(strftime('%s', 'now'))`)
      .notNull(),
    updatedAt: integer()
      .default(sql`(strftime('%s', 'now'))`)
      .$onUpdate(() => sql`(strftime('%s', 'now'))`)
      .notNull(),

    metadata: text({ mode: "json" })
      .notNull()
      .$type<Record<string, string | number | boolean>>()
      .default({}),
  },
  (table) => [index("idx_keys_provider").on(table.hash, table.provider)],
);

export const keyStats = sqliteTable(
  "key_stats",
  {
    id: text().primaryKey(),
    hash: text()
      .notNull()
      .references(() => keys.hash, { onDelete: "cascade", onUpdate: "cascade" }),
    modelFamily: text()
      .notNull()
      .references(() => modelFamilies.id, { onDelete: "cascade", onUpdate: "cascade" }),

    inputTokens: integer().notNull().default(0),
    outputTokens: integer().notNull().default(0),
    reasoningTokens: integer().notNull().default(0),

    prompts: integer().notNull().default(0),
  },
  (table) => [uniqueIndex("idx_key_stats_hash_model_family").on(table.hash, table.modelFamily)],
);

// Define relations
export const keysRelations = relations(keys, ({ many }) => ({
  stats: many(keyStats),
}));

export const keyStatsRelations = relations(keyStats, ({ one }) => ({
  key: one(keys, {
    fields: [keyStats.hash],
    references: [keys.hash],
  }),
  modelFamily: one(modelFamilies, {
    fields: [keyStats.modelFamily],
    references: [modelFamilies.id],
  }),
}));
