import { createClient } from "@libsql/client";
import { drizzle } from "drizzle-orm/libsql";

import { config } from "@/config";
import * as schema from "./schema";

export * from "drizzle-orm";
export { schema };

export const db = drizzle({
  client: createClient(
    config.database.type === "turso"
      ? { url: config.database.url, authToken: config.database.authToken }
      : { url: "file:" + config.database.path },
  ),
  schema,
});
