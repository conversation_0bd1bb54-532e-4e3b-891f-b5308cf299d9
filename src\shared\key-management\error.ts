export class HttpError extends Error {
  constructor(public code: number, message: string) {
    super(message);
    this.name = "HttpError";
  }
}

export class CheckFailedError extends Error {
  constructor(message: string, public status: number, public response: Response) {
    super(message);
    this.name = "CheckFailedError";
  }
}

export class PaymentRequiredError extends HttpError {
  constructor(message: string) {
    super(402, message);
    this.name = "PaymentRequiredError";
  }
}
