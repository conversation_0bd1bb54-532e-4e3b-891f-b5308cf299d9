// gocatch.ts

/**
 * A tuple representing the result of an operation that can fail.
 * On success, it's `[data, null]`.
 * On failure, it's `[null, error]`.
 * @template T The type of the data on success.
 * @template E The specific type of the Error on failure.
 */
export type GoResult<T, E extends Error> = [T, null] | [null, E | Error];

/**
 * A tuple representing the asynchronous result of an operation that can fail.
 * This is a Promise that resolves to a GoResult.
 * @template T The type of the data on success.
 * @template E The specific type of the Error on failure.
 */
export type GoResultAsync<T, E extends Error> = Promise<GoResult<T, E>>;

/**
 * Wraps a synchronous function in a try-catch block and returns a GoResult tuple.
 *
 * @template T The return type of the function `fn`.
 * @template E A custom error class that extends Error.
 * @param {() => T} fn The synchronous function to execute.
 * @param {new (...args: unknown[]) => E} [errorClass] An optional custom error class to check for.
 * @returns {GoResult<T, E>} A tuple of `[data, error]`.
 */
export function tryCatchSync<T, E extends Error>(
  fn: () => T,
  errorClass?: new (...args: unknown[]) => E,
): GoResult<T, E> {
  try {
    const data = fn();
    return [data, null];
  } catch (error) {
    if (errorClass && error instanceof errorClass) {
      return [null, error];
    }
    if (error instanceof Error) {
      return [null, error];
    }
    // If the thrown value is not an Error instance, wrap it in a new Error.
    return [null, new Error(String(error))];
  }
}

/**
 * Wraps an asynchronous function (that returns a Promise) in a try-catch block
 * and returns a Promise that resolves to a GoResult tuple.
 *
 * @template T The resolved type of the Promise returned by `promiseFn`.
 * @template E A custom error class that extends Error.
 * @param {() => Promise<T> | Promise<T>} promiseFn The asynchronous function to execute.
 * @param {new (...args: unknown[]) => E} [errorClass] An optional custom error class to check for.
 * @returns {GoResultAsync<T, E>} A Promise resolving to a tuple of `[data, error]`.
 */
export async function tryCatch<T, E extends Error>(
  promiseFn: Promise<T> | (() => Promise<T>),
  errorClass?: new (...args: any[]) => E,
): GoResultAsync<T, E> {
  try {
    const data = await (typeof promiseFn === "function" ? promiseFn() : promiseFn);
    return [data, null];
  } catch (error) {
    if (errorClass && error instanceof errorClass) {
      return [null, error];
    }
    if (error instanceof Error) {
      return [null, error];
    }
    // If the thrown value is not an Error instance, wrap it in a new Error.
    return [null, new Error(String(error))];
  }
}
