import { z } from "zod";

export const GeminiGenerateContentResponseSchema = z.object({
  candidates: z.array(
    z.object({
      content: z.object({
        parts: z.object({ text: z.string(), thought: z.boolean().optional() }).array(),
        role: z.literal("model"),

        functionResponse: z.record(z.string(), z.unknown()).optional(),
        functionCall: z.record(z.string(), z.unknown()).optional(),
      }),
      finishReason: z
        .enum([
          "STOP",
          "MAX_TOKENS",
          "SAFETY",
          "OTHER",
          "PROHIBITED_CONTENT",
          "MALFORMED_FUNCTION_CALL",
          "IMAGE_SAFETY",
          "UNEXPECTED_TOOL_CALL",
        ])
        .nullish(),
      index: z.int(),
      safetyRatings: z.array(
        z.object({
          blocked: z.boolean().optional(),
          category: z.enum([
            "HARM_CATEGORY_HARASSMENT",
            "HARM_CATEGORY_HATE_SPEECH",
            "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "HARM_CATEGORY_DANGEROUS_CONTENT",
            "HARM_CATEGORY_CIVIC_INTEGRITY",
          ]),
          probability: z.enum(["NEGLIGIBLE", "LOW", "MEDIUM", "HIGH"]),
        }),
      ),
    }),
  ),
  modelVersion: z.string(),

  usageMetadata: z.object({
    promptTokenCount: z.number().int(),
    candidatesTokenCount: z.number().int(),
    totalTokenCount: z.number().int(),
    thoughtsTokenCount: z.number().int().optional(),
  }),
});

export type GeminiGenerateContentResponse = z.infer<typeof GeminiGenerateContentResponseSchema>;
export type GeminiStreamGenerateContentResponse = z.infer<
  typeof GeminiGenerateContentResponseSchema
>;
