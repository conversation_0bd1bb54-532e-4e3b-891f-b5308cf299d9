import { tryCatch } from "@/shared/utils/try-catch";

import { Key<PERSON>heckerBase } from "@/shared/key-management/base-checker";
import { CheckFailedError } from "@/shared/key-management/error";
import { decrypt } from "@/shared/key-management/utils";

import { type Deepseek<PERSON><PERSON>, DeepseekKeyProvider } from "./provider";

const GET_MODELS_URL = "https://api.deepseek.com/v1/models";
const POST_CHAT_COMPLETIONS_URL = "https://api.deepseek.com/beta/chat/completions";

const MIN_CHECK_INTERVAL = 3 * 1000; // 3 seconds
const KEY_CHECK_PERIOD = 60 * 60 * 1000; // 1 hour

type GetModelsResponse = {
  data: [{ id: string }];
};

type DeepSeekError = {
  error: { type: string; code: string; param: unknown; message: string };
  // This error only appear if we have too many failed requests
  error_msg: string | null;
};

export class <PERSON><PERSON>k<PERSON><PERSON><PERSON>he<PERSON> extends KeyCheckerBase<DeepseekKey> {
  constructor({ updateFn }: { updateFn: typeof DeepseekKeyProvider.prototype.update }) {
    super({
      updateFn,
      provider: DeepseekKeyProvider.provider,
      keyCheckPeriod: KEY_CHECK_PERIOD,
      minCheckInterval: MIN_CHECK_INTERVAL,
    });
  }

  protected override createHeaders(key: DeepseekKey, extras: Record<string, string> = {}): Headers {
    const headers = new Headers();
    headers.set("Authorization", `Bearer ${decrypt(key.encryptedKey)}`);

    Object.entries(extras).forEach(([key, value]) => {
      headers.set(key, value);
    });

    return headers;
  }

  private async getModelIds(key: DeepseekKey): Promise<string[]> {
    const response = await fetch(GET_MODELS_URL, { headers: this.createHeaders(key) });

    if (!response.ok) {
      throw new CheckFailedError("Failed to get models", response.status, response);
    }

    const [data, err] = await tryCatch(() => response.json() as Promise<GetModelsResponse>);
    if (err) {
      throw new CheckFailedError("Failed to parse models", response.status, response);
    }

    const modelIds = new Set<string>(data.data.map(({ id }) => id));
    return Array.from(modelIds);
  }

  private async testLiveness(key: DeepseekKey) {
    const payload = {
      model: "deepseek-chat",
      max_tokens: 1,
      messages: [{ role: "user", content: "hello" }],
    };

    const response = await fetch(POST_CHAT_COMPLETIONS_URL, {
      method: "POST",
      headers: this.createHeaders(key, { "Content-Type": "application/json" }),
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new CheckFailedError("Failed to test liveness", response.status, response);
    }
  }

  protected override async testKeyOrFail(key: DeepseekKey, forced?: boolean) {
    const requireFullCheck = key.status === "unknown" || forced;
    const requireModelCheck =
      key.lastModelsCheckedAt < Date.now() - 24 * 60 * 60 * 1000 && !requireFullCheck;

    const updates: Partial<DeepseekKey> = {};

    if (requireFullCheck) {
      const [, modelIds] = await Promise.all([this.testLiveness(key), this.getModelIds(key)]);

      updates.status = "working";
      updates.metadata = {};
      updates.modelIds = modelIds.filter((id) => !id.includes("ft")).sort();

      updates.lastModelsCheckedAt = Date.now();
    }

    if (requireModelCheck) {
      updates.modelIds = await this.getModelIds(key);
      updates.lastModelsCheckedAt = Date.now();
    }

    return { ...updates, nextCheckAt: null };
  }

  protected override async handleCheckingError(
    key: DeepseekKey,
    err: Error | CheckFailedError,
  ): Promise<Partial<DeepseekKey>> {
    if (err instanceof CheckFailedError) {
      const [parsed, parseErr] = await tryCatch(
        () => err.response.json() as Promise<DeepSeekError>,
      );

      if (parseErr || !parsed || !parsed.error) {
        this.logger.error(
          { key: key.hash, error: err, parseErr },
          "Failed to parse error response while checking key. Scheduling recheck in ~60s.",
        );

        return { nextCheckAt: Date.now() + 60 * 1000 };
      }

      const status = err.status;

      switch (true) {
        case status === 401 || parsed.error.message.includes("no such user"): {
          this.logger.warn(
            { key: key.hash, error: parsed },
            "Key is invalid or revoked. Disabling key.",
          );

          return { disabledAt: Date.now(), disabledBy: "key-checker", status: "revoked" };
        }

        case status === 402 && parsed.error.message.includes("Insufficient Balance"): {
          this.logger.warn(
            { key: key.hash, rateLimitType: parsed.error.type, error: parsed },
            "Key returned 402 error due to insufficient balance. Disabling key.",
          );

          return { disabledAt: Date.now(), disabledBy: "key-checker", status: "out_of_quota" };
        }

        case status === 429 && parsed.error_msg?.includes("Multiple 401 errors detected."): {
          this.logger.warn(
            { key: key.hash, error: parsed },
            "Key returned 429 error due to too many failed requests. Waiting for a minute before trying again.",
          );

          return { nextCheckAt: Date.now() + 60 * 1000 };
        }

        default: {
          this.logger.error(
            { key: key.hash, status, error: parsed },
            "Encountered unexpected error status while checking key. This may indicate a change in the API; please report this.",
          );
          return {};
        }
      }
    }

    this.logger.error(
      { key: key.hash, error: err.message },
      "Network error while checking key; retrying in ~60s.",
    );
    return { nextCheckAt: Date.now() + 60 * 1000 };
  }
}
