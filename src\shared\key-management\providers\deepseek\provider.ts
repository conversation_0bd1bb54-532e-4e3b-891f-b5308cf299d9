import { type Key } from "@/shared/key-management";
import { BaseKeyProvider } from "@/shared/key-management/base-provider";

import { config } from "@/config";

import { Deepseek<PERSON>eyChecker } from "./checker";

export interface DeepseekKey extends Key {
  readonly provider: "deepseek";

  metadata: {};
}

export class DeepseekKeyProvider extends BaseKeyProvider<DeepseekKey> {
  static override readonly provider = "deepseek" as const;

  protected override getProviderPrefix(): string {
    return "dps";
  }

  constructor() {
    super(DeepseekKeyProvider.provider);
  }

  public override async init(): Promise<void> {
    const checker = new DeepseekKeyChecker({ updateFn: this.update.bind(this) });
    await super.init({ checker });
  }

  protected getSeedKeysFromConfig(): string[] {
    return config.keys.deepseek;
  }

  protected override serializeNewKeyMetadataForInsert(): DeepseekKey["metadata"] {
    return {};
  }

  protected override matchesModel(k: <PERSON><PERSON><PERSON><PERSON><PERSON>, modelId: string): boolean {
    if (!k.modelIds || (k.modelIds.length > 0 && !k.modelIds.includes(modelId))) return false;
    return true;
  }

  protected override buildUsageStatsModelFamily(modelId: string): string {
    return modelId;
  }
}
