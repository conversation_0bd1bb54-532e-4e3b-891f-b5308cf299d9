import { useCallback, useEffect, useState } from "react";
import { z } from "zod";

type UseLocalStorageOptions<T> = {
  key: string;
  schema: z.ZodType<T>;
  defaultValue?: T;
};

type UseLocalStorageReturn<T> = {
  data: T | null;
  setData: (value: T | null) => void;
  clearData: () => void;
  isLoading: boolean;
};

/**
 * Custom hook for interacting with localStorage with Zod validation
 * @param options Configuration object with key, schema, and optional defaultValue
 * @returns Object with data, setData, clearData, and isLoading
 */
export function useLocalStorage<T>({
  key,
  schema,
  defaultValue,
}: UseLocalStorageOptions<T>): UseLocalStorageReturn<T> {
  const [data, setDataState] = useState<T | null>(defaultValue ?? null);
  const [isLoading, setIsLoading] = useState(true);

  // Load data from localStorage on mount
  useEffect(() => {
    try {
      const item = localStorage.getItem(key);
      if (item === null) {
        setDataState(defaultValue ?? null);
        setIsLoading(false);
        return;
      }

      const parsed = JSON.parse(item);
      const validated = schema.safeParse(parsed);

      if (validated.success) {
        setDataState(validated.data);
      } else {
        console.warn(`Invalid data in localStorage for key "${key}":`, validated.error);
        // Clear invalid data
        localStorage.removeItem(key);
        setDataState(defaultValue ?? null);
      }
    } catch (error) {
      console.error(`Error reading from localStorage for key "${key}":`, error);
      // Clear corrupted data
      localStorage.removeItem(key);
      setDataState(defaultValue ?? null);
    } finally {
      setIsLoading(false);
    }
  }, [key, schema, defaultValue]);

  const setData = useCallback(
    (value: T | null) => {
      try {
        if (value === null) {
          localStorage.removeItem(key);
          setDataState(null);
          return;
        }

        // Validate before storing
        const validated = schema.safeParse(value);
        if (!validated.success) {
          console.error(`Invalid data provided for key "${key}":`, validated.error);
          return;
        }

        localStorage.setItem(key, JSON.stringify(validated.data));
        setDataState(validated.data);
      } catch (error) {
        console.error(`Error writing to localStorage for key "${key}":`, error);
      }
    },
    [key, schema]
  );

  const clearData = useCallback(() => {
    try {
      localStorage.removeItem(key);
      setDataState(defaultValue ?? null);
    } catch (error) {
      console.error(`Error clearing localStorage for key "${key}":`, error);
    }
  }, [key, defaultValue]);

  return {
    data,
    setData,
    clearData,
    isLoading,
  };
}
