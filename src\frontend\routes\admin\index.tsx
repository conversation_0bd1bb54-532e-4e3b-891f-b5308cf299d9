import { useTRPC } from "@/lib/trpc/client";
import { useQuery } from "@tanstack/react-query";
import { Navigate } from "react-router";

export function IndexAdminPage() {
  const trpc = useTRPC();

  const { data } = useQuery(trpc.auth.isAuthenticated.queryOptions());

  if (data && data.status === "authenticated") return <Navigate to="/admin/dashboard" replace />;
  return <Navigate to="/admin/login" replace />;
}
