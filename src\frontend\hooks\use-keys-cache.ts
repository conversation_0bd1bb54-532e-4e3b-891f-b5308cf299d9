import { createSelectSchema } from "drizzle-zod";
import { useCallback, useEffect } from "react";
import { z } from "zod";

import { keys } from "@/shared/database/schema/keys";
import type { Key } from "@/shared/key-management";

import { useLocalStorage } from "./use-local-storage";

const CACHE_KEY = "admin-keys-cache-v1";
const CACHE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes

export const KeysArraySchema = z.array(createSelectSchema(keys));

type CachedKeysData = {
  keys: Key[];
  timestamp: number;
};

const CachedKeysSchema = z.object({
  keys: KeysArraySchema,
  timestamp: z.number(),
});

type UseKeysCacheOptions = {
  freshData?: Key[];
  isLoading?: boolean;
};

type UseKeysCacheReturn = {
  cachedKeys: Key[] | null;
  updateCache: (keys: Key[]) => void;
  clearCache: () => void;
  isCacheValid: boolean;
  isLoadingCache: boolean;
};

/**
 * Custom hook for caching keys data in localStorage
 * Provides instant loading from cache while fresh data is being fetched
 */
export function useKeysCache({
  freshData,
  isLoading = false,
}: UseKeysCacheOptions = {}): UseKeysCacheReturn {
  const {
    data: cachedData,
    setData: setCachedData,
    clearData: clearCachedData,
    isLoading: isLoadingCache,
  } = useLocalStorage({
    key: CACHE_KEY,
    schema: CachedKeysSchema,
  });

  // Check if cache is still valid (not expired)
  const isCacheValid = cachedData ? Date.now() - cachedData.timestamp < CACHE_EXPIRY_MS : false;

  const updateCache = useCallback(
    (keys: Key[]) => {
      const cacheData: CachedKeysData = {
        keys,
        timestamp: Date.now(),
      };
      setCachedData(cacheData);
    },
    [setCachedData],
  );

  // Update cache when fresh data is available
  useEffect(() => {
    if (freshData && !isLoading) {
      updateCache(freshData);
    }
  }, [freshData, isLoading, updateCache]);

  const clearCache = useCallback(() => {
    clearCachedData();
  }, [clearCachedData]);

  return {
    cachedKeys: cachedData?.keys ?? null,
    updateCache,
    clearCache,
    isCacheValid,
    isLoadingCache,
  };
}
