import type { LLM_Providers } from "../providers";

export type ModelData<T extends LLM_Providers> = {
  id: string;
  object: "model";
  created: number;
  owned_by: T;
  permission: [
    {
      id: `modelperm-${string}`;
      object: "model_permission";
      created: string;
      organization: "*";
      group: null;
      is_blocking: false;
    },
  ];
  root: T;
  parent: null;
};

export function createModelList<T extends LLM_Providers>(
  models: string[],
  service: T,
  filterFn: (model: string) => boolean = () => true,
) {
  return models
    .sort()
    .filter(filterFn)
    .map(
      (id): ModelData<T> => ({
        id,
        object: "model",
        created: new Date().getTime(),
        owned_by: service,
        permission: [
          {
            id: `modelperm-${id}`,
            object: "model_permission",
            created: new Date().getTime().toString(),
            organization: "*",
            group: null,
            is_blocking: false,
          },
        ],
        root: service,
        parent: null,
      }),
    );
}
